{"name": "quiz-maker-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-query": "^3.39.3", "react-hook-form": "^7.48.2", "react-dropzone": "^14.2.3", "axios": "^1.6.2", "zustand": "^4.4.7", "zod": "^3.22.4", "@hookform/resolvers": "^3.3.2", "clsx": "^2.0.0", "lucide-react": "^0.294.0", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.16", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "date-fns": "^2.30.0", "recharts": "^2.8.0", "@stripe/stripe-js": "^2.2.0", "@stripe/react-stripe-js": "^2.4.0", "jspdf": "^2.5.1", "html2canvas": "^1.4.1"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/react-syntax-highlighter": "^15.5.10", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^4.5.0"}}