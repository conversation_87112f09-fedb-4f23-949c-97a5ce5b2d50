# AI-Powered Quiz Generator for Teachers

A production-ready web application that allows teachers to generate quiz questions automatically from lesson content using advanced NLP and AI technologies.

## 🚀 Features

### Core Features
- **Smart Question Generation**: Upload documents (PDF, DOCX, TXT) or paste text to generate multiple-choice, true/false, and short-answer questions
- **AI-Powered Analysis**: Uses advanced NLP to extract key concepts and generate relevant questions
- **Difficulty Scaling**: Automatically generates questions at Easy, Medium, and Hard difficulty levels
- **Real-time Preview**: Edit and customize generated questions before finalizing
- **Export Options**: Export quizzes as PDF or Word documents with professional formatting

### User Management
- **Secure Authentication**: JWT-based authentication with password reset
- **Role-based Access**: Teacher and School Admin roles
- **Dashboard**: Manage quizzes, view history, and track usage

### Monetization (Freemium Model)
- **Free Tier**: 5 quiz generations per month
- **Premium**: Unlimited access, PDF export, analytics dashboard
- **School Plan**: Multi-user management, admin dashboard, bulk operations

## 🛠 Technology Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for fast development and building
- **Tailwind CSS** for responsive design
- **React Query** for state management and API calls
- **React Hook Form** with Zod validation

### Backend
- **Flask** with Python 3.11+
- **SQLAlchemy** ORM with PostgreSQL
- **Redis** for caching and session management
- **Flask-JWT-Extended** for authentication
- **Celery** for background tasks

### AI/NLP
- **Hugging Face Transformers** for question generation
- **spaCy** for NLP preprocessing
- **OpenAI API** for enhanced question generation (optional)

### Infrastructure
- **PostgreSQL** database
- **Redis** for caching
- **Docker** for containerization
- **Stripe** for payment processing

## 📁 Project Structure

```
quiz-maker/
├── frontend/                 # React application
│   ├── src/
│   │   ├── components/      # Reusable UI components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom React hooks
│   │   ├── services/       # API services
│   │   ├── store/          # State management
│   │   └── utils/          # Utility functions
│   ├── public/
│   └── package.json
├── backend/                  # Flask API
│   ├── app/
│   │   ├── models/         # Database models
│   │   ├── routes/         # API routes
│   │   ├── services/       # Business logic
│   │   ├── utils/          # Utility functions
│   │   └── ai/             # AI/NLP modules
│   ├── migrations/         # Database migrations
│   ├── tests/              # Backend tests
│   └── requirements.txt
├── shared/                   # Shared utilities
├── docs/                    # Documentation
├── scripts/                 # Setup scripts
└── docker-compose.yml      # Development environment
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- Python 3.11+
- PostgreSQL 14+
- Redis 6+
- Docker (optional)

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd quiz-maker
   ```

2. **Backend Setup**
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. **Frontend Setup**
   ```bash
   cd frontend
   npm install
   ```

4. **Database Setup**
   ```bash
   # Create PostgreSQL database
   createdb quiz_maker_dev
   
   # Run migrations
   cd backend
   flask db upgrade
   ```

5. **Environment Variables**
   ```bash
   # Copy environment files
   cp backend/.env.example backend/.env
   cp frontend/.env.example frontend/.env
   
   # Edit the .env files with your configuration
   ```

6. **Start Development Servers**
   ```bash
   # Terminal 1: Backend
   cd backend
   flask run
   
   # Terminal 2: Frontend
   cd frontend
   npm run dev
   
   # Terminal 3: Redis (if not using Docker)
   redis-server
   ```

### Docker Development (Alternative)

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## 📚 API Documentation

Once the backend is running, visit:
- Swagger UI: `http://localhost:5000/docs`
- ReDoc: `http://localhost:5000/redoc`

## 🧪 Testing

### Backend Tests
```bash
cd backend
pytest
```

### Frontend Tests
```bash
cd frontend
npm test
```

## 🚀 Deployment

### Production Build
```bash
# Frontend
cd frontend
npm run build

# Backend
cd backend
pip install -r requirements.txt
flask db upgrade
```

### Docker Production
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## 📞 Support

For support, email <EMAIL> or join our Slack channel.
