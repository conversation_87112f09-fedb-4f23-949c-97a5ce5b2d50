version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: quiz_maker_dev
      POSTGRES_USER: quiz_user
      POSTGRES_PASSWORD: quiz_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U quiz_user -d quiz_maker_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=development
      - FLASK_DEBUG=1
      - DATABASE_URL=**************************************************/quiz_maker_dev
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=dev-secret-key-change-in-production
      - JWT_SECRET_KEY=jwt-secret-key-change-in-production
    volumes:
      - ./backend:/app
      - ./shared:/app/shared
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: flask run --host=0.0.0.0 --port=5000

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    environment:
      - VITE_API_URL=http://localhost:5000
      - VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key
    volumes:
      - ./frontend:/app
      - ./shared:/app/shared
      - /app/node_modules
    depends_on:
      - backend
    command: npm run dev -- --host 0.0.0.0

  # Celery Worker (for background tasks)
  celery:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - DATABASE_URL=**************************************************/quiz_maker_dev
    volumes:
      - ./backend:/app
      - ./shared:/app/shared
    depends_on:
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
    command: celery -A app.celery worker --loglevel=info

  # Celery Beat (for scheduled tasks)
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - DATABASE_URL=**************************************************/quiz_maker_dev
    volumes:
      - ./backend:/app
      - ./shared:/app/shared
    depends_on:
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
    command: celery -A app.celery beat --loglevel=info

volumes:
  postgres_data:
  redis_data:

networks:
  default:
    name: quiz_maker_network
