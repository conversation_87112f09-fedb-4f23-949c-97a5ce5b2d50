# Flask Configuration
FLASK_ENV=development
FLASK_DEBUG=1
SECRET_KEY=your-secret-key-change-in-production
JWT_SECRET_KEY=your-jwt-secret-key-change-in-production

# Database Configuration
DATABASE_URL=postgresql://quiz_user:quiz_password@localhost:5432/quiz_maker_dev
SQLALCHEMY_TRACK_MODIFICATIONS=False

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Email Configuration (for password reset)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_DEFAULT_SENDER=<EMAIL>

# AI/NLP Configuration
OPENAI_API_KEY=your-openai-api-key
HUGGINGFACE_API_KEY=your-huggingface-api-key

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# File Upload Configuration
MAX_CONTENT_LENGTH=16777216  # 16MB
UPLOAD_FOLDER=uploads
ALLOWED_EXTENSIONS=txt,pdf,docx

# Rate Limiting
RATELIMIT_STORAGE_URL=redis://localhost:6379/1

# Monitoring
SENTRY_DSN=your-sentry-dsn

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
